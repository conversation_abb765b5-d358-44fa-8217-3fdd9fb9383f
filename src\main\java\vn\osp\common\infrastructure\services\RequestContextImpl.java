package vn.osp.common.infrastructure.services;

import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import vn.osp.common.application.constants.HttpConst;
import vn.osp.common.application.services.RequestContext;
import vn.osp.common.application.services.TenantClientService;
import vn.osp.common.domain.helpers.StringHelper;
import vn.osp.common.domain.helpers.UuidHelper;

import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
public class RequestContextImpl implements RequestContext {
    private final HttpServletRequest request;
    private final TenantClientService tenantClientService;

    @Override
    public UUID getTenantId() {
        UUID tenantId = null;
        String tenantIdStr = request.getHeader(HttpConst.TENANT_ID_HEADER);
        if (StringHelper.isNotEmpty(tenantIdStr)) {
            tenantId = UuidHelper.fromString(tenantIdStr);
        }

        if (UuidHelper.isEmpty(tenantId)) {
            // TODO: Implement logic to get tenant by name from request
            // String tenantName = request.getHeader("X-Tenant-Name");
            // if (StringHelper.isNotEmpty(tenantName)) {
            //     TenantDto tenant = tenantClientService.getTenantByName(tenantName);
            //     if (tenant != null) {
            //         tenantId = tenant.getId();
            //     }
            // }
        }

        return tenantId;
    }

    @Override
    public UUID getUserId() {
        String userIdStr = request.getHeader(HttpConst.USER_ID_HEADER);
        if (StringHelper.isNotEmpty(userIdStr)) {
            return UuidHelper.fromString(userIdStr);
        }
        return null;
    }

    @Override
    public String getUsername() {
        String username = request.getHeader(HttpConst.AUTHENTICATED_USER_HEADER);
        return StringHelper.isNotEmpty(username) ? username : "";
    }

    @Override
    public List<String> getRoles() {
        String rolesStr = request.getHeader(HttpConst.USER_ROLES_HEADER);
        if (StringHelper.isNotEmpty(rolesStr)) {
            return List.of(rolesStr.split(","));
        }
        return List.of();
    }
}
