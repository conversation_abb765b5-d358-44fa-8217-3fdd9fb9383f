package vn.osp.common.infrastructure.services;

import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import vn.osp.common.application.constants.HttpConst;
import vn.osp.common.application.services.RequestContext;
import vn.osp.common.application.services.TenantClientService;
import vn.osp.common.domain.helpers.StringHelper;
import vn.osp.common.domain.helpers.UuidHelper;

import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
public class RequestContextImpl implements RequestContext {
    private final HttpServletRequest request;
    private final TenantClientService tenantClientService;

    @Override
    public UUID getTenantId() {
        UUID tenantId = null;
        String tenantIdStr = request.getHeader(HttpConst.TENANT_ID_HEADER);
        if (StringHelper.isNotEmpty(tenantIdStr)) {
            tenantId = UuidHelper.fromString(tenantIdStr);
        }

        if (UuidHelper.isEmpty(tenantId)) {
            String tenantName;
            tenantClientService.getTenantByName(tenantName)
        }

        return null;
    }

    @Override
    public UUID getUserId() {
        return null;
    }

    @Override
    public String getUsername() {
        return "";
    }

    @Override
    public List<String> getRoles() {
        return List.of();
    }
}
