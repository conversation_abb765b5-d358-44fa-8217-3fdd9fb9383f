package vn.osp.common.infrastructure.repositories;

import jakarta.persistence.EntityManager;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import vn.osp.common.domain.entity.ExampleEntity;
import vn.osp.common.infrastructure.annotations.DisableGlobalFilters;
import vn.osp.common.infrastructure.annotations.EnableGlobalFilters;

import java.util.List;
import java.util.UUID;

/**
 * Example repository để minh họa cách sử dụng global filters.
 * Repository này extend từ BaseFilteredRepository để có filtering tự động.
 */
@Repository
public interface ExampleRepository extends JpaRepository<ExampleEntity, UUID> {

    /**
     * Tìm theo name - sẽ tự động áp dụng global filters.
     */
    List<ExampleEntity> findByNameContaining(String name);

    /**
     * Tìm theo name với filters được enable explicitly.
     */
    @EnableGlobalFilters(reason = "Ensure data security for name search")
    @Query("SELECT e FROM ExampleEntity e WHERE e.name LIKE %:name%")
    List<ExampleEntity> findByNameWithFilters(@Param("name") String name);

    /**
     * Tìm tất cả records bao gồm cả deleted - cần disable soft delete filter.
     */
    @DisableGlobalFilters(
        filters = {"softDeleteFilter"}, 
        reason = "Administrative function to view deleted records"
    )
    @Query("SELECT e FROM ExampleEntity e WHERE e.name LIKE %:name%")
    List<ExampleEntity> findByNameIncludingDeleted(@Param("name") String name);

    /**
     * Tìm tất cả records của tất cả tenant - cần disable tenant filter.
     */
    @DisableGlobalFilters(
        filters = {"tenantFilter"}, 
        reason = "Cross-tenant administrative report"
    )
    @Query("SELECT e FROM ExampleEntity e WHERE e.name LIKE %:name%")
    List<ExampleEntity> findByNameAcrossAllTenants(@Param("name") String name);

    /**
     * Tìm tất cả records bao gồm cả disabled - cần disable enabled filter.
     */
    @DisableGlobalFilters(
        filters = {"enabledFilter"}, 
        reason = "Administrative function to view disabled records"
    )
    @Query("SELECT e FROM ExampleEntity e WHERE e.name LIKE %:name%")
    List<ExampleEntity> findByNameIncludingDisabled(@Param("name") String name);

    /**
     * Tìm tất cả records không có filter nào - cần disable tất cả filters.
     */
    @DisableGlobalFilters(reason = "System maintenance operation requiring full data access")
    @Query("SELECT e FROM ExampleEntity e")
    List<ExampleEntity> findAllWithoutAnyFilters();

    /**
     * Đếm số lượng records theo tenant cụ thể.
     */
    @Query("SELECT COUNT(e) FROM ExampleEntity e WHERE e.tenantId = :tenantId")
    long countByTenantId(@Param("tenantId") UUID tenantId);

    /**
     * Tìm records đã bị xóa của tenant hiện tại.
     */
    @DisableGlobalFilters(
        filters = {"softDeleteFilter"}, 
        reason = "Tenant-specific deleted records recovery"
    )
    @Query("SELECT e FROM ExampleEntity e WHERE e.isDeleted = true")
    List<ExampleEntity> findDeletedRecords();
}

/**
 * Implementation class cho custom repository methods.
 */
@Repository
class ExampleRepositoryImpl extends BaseFilteredRepository<ExampleEntity, UUID> {

    public ExampleRepositoryImpl(EntityManager entityManager) {
        super(ExampleEntity.class, entityManager);
    }

    /**
     * Custom method sử dụng BaseFilteredRepository features.
     */
    public List<ExampleEntity> findActiveRecordsOnly() {
        // Sẽ tự động áp dụng tất cả filters
        return findAll();
    }

    /**
     * Custom method để tìm tất cả records bao gồm deleted.
     */
    public List<ExampleEntity> findAllIncludingDeleted() {
        return findAllWithoutFilters();
    }

    /**
     * Custom method để tìm records chỉ với soft delete filter.
     */
    public List<ExampleEntity> findAllOnlySoftDeleteFiltered() {
        return findAllOnlyWithSoftDeleteFilter();
    }

    /**
     * Custom method để tìm records chỉ với tenant filter.
     */
    public List<ExampleEntity> findAllOnlyTenantFiltered() {
        return findAllOnlyWithTenantFilter();
    }
}
