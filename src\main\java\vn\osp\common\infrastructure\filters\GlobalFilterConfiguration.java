package vn.osp.common.infrastructure.filters;

import jakarta.annotation.PostConstruct;
import jakarta.persistence.EntityManagerFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.engine.spi.FilterDefinition;
import org.hibernate.engine.spi.SessionFactoryImplementor;
import org.hibernate.type.StandardBasicTypes;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Configuration tự động đăng ký global filter definitions cho Hibernate.
 * Không cần annotate từng entity, filter sẽ được áp dụng tự động.
 */
@Configuration
@RequiredArgsConstructor
@Slf4j
public class GlobalFilterConfiguration {
    @Lazy
    private final EntityManagerFactory entityManagerFactory;

    @PostConstruct
    public void registerGlobalFilters() {
        try {
            SessionFactoryImplementor sessionFactory = entityManagerFactory.unwrap(SessionFactoryImplementor.class);
            
            // Đăng ký filter definitions
            registerFilterDefinitions(sessionFactory);
            
            log.info("Global filter definitions registered successfully");
        } catch (Exception e) {
            log.error("Failed to register global filter definitions: {}", e.getMessage(), e);
        }
    }

    /**
     * Đăng ký các filter definitions vào SessionFactory.
     */
    private void registerFilterDefinitions(SessionFactoryImplementor sessionFactory) {
        Map<String, FilterDefinition> filterDefinitions = createFilterDefinitions();
        
        // Lấy metadata và đăng ký filters
        try {
            // Sử dụng reflection để truy cập internal methods nếu cần
            for (Map.Entry<String, FilterDefinition> entry : filterDefinitions.entrySet()) {
                String filterName = entry.getKey();
                FilterDefinition filterDef = entry.getValue();
                
                // Thêm filter definition vào session factory
                // Note: Trong Hibernate 6+, cách tiếp cận này có thể cần điều chỉnh
                sessionFactory.getAutoEbledFilters().add(filterDef);
                log.debug("Registering filter definition: {}", filterName);
            }
        } catch (Exception e) {
            log.warn("Could not register filter definitions via reflection: {}", e.getMessage());
        }
    }

    /**
     * Tạo các filter definitions.
     */
    private Map<String, FilterDefinition> createFilterDefinitions() {
        Map<String, FilterDefinition> filters = new HashMap<>();

        // Soft Delete Filter
        Map<String, org.hibernate.type.Type> softDeleteParams = new HashMap<>();
        FilterDefinition softDeleteFilter = new FilterDefinition(
                GlobalFilterManager.SOFT_DELETE_FILTER,
                "isDeleted = false", // Condition cho cả trường isDeleted và deleted
                softDeleteParams
        );
        filters.put(GlobalFilterManager.SOFT_DELETE_FILTER, softDeleteFilter);

        // Tenant Filter
        Map<String, org.hibernate.type.Type> tenantParams = new HashMap<>();
        tenantParams.put("tenantId", StandardBasicTypes.UUID_CHAR);
        FilterDefinition tenantFilter = new FilterDefinition(
                GlobalFilterManager.TENANT_FILTER,
                "tenantId = :tenantId OR tenantId IS NULL", // Cho phép shared data (tenantId = null)
                Map.of("tenantId", UUID.class)
        );
        filters.put(GlobalFilterManager.TENANT_FILTER, tenantFilter);

        // Enabled Filter
        Map<String, org.hibernate.type.Type> enabledParams = new HashMap<>();
        FilterDefinition enabledFilter = new FilterDefinition(
                GlobalFilterManager.ENABLED_FILTER,
                "enabled = true",
                enabledParams
        );
        filters.put(GlobalFilterManager.ENABLED_FILTER, enabledFilter);

        return filters;
    }
}
