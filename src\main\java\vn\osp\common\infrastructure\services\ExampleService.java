package vn.osp.common.infrastructure.services;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.osp.common.domain.entity.ExampleEntity;
import vn.osp.common.infrastructure.annotations.DisableGlobalFilters;
import vn.osp.common.infrastructure.annotations.EnableGlobalFilters;
import vn.osp.common.infrastructure.repositories.ExampleRepository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Example service để minh họa cách sử dụng global filters trong service layer.
 * Service này extend từ BaseFilteredService để có filtering utilities.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ExampleService extends BaseFilteredService {

    private final ExampleRepository exampleRepository;

    /**
     * <PERSON><PERSON>y tất cả active records - tự động áp dụng tất cả filters.
     */
    @Transactional(readOnly = true)
    @EnableGlobalFilters(reason = "Standard business operation")
    public List<ExampleEntity> getAllActiveRecords() {
        enableGlobalFilters();
        return exampleRepository.findAll();
    }

    /**
     * Lấy record theo ID - tự động áp dụng tất cả filters.
     */
    @Transactional(readOnly = true)
    public Optional<ExampleEntity> getById(UUID id) {
        enableGlobalFilters();
        return exampleRepository.findById(id);
    }

    /**
     * Tạo mới record.
     */
    @Transactional
    public ExampleEntity create(ExampleEntity entity) {
        enableGlobalFilters();
        return exampleRepository.save(entity);
    }

    /**
     * Cập nhật record.
     */
    @Transactional
    public ExampleEntity update(ExampleEntity entity) {
        enableGlobalFilters();
        return exampleRepository.save(entity);
    }

    /**
     * Xóa mềm record.
     */
    @Transactional
    public void softDelete(UUID id) {
        enableGlobalFilters();
        Optional<ExampleEntity> entity = exampleRepository.findById(id);
        if (entity.isPresent()) {
            ExampleEntity record = entity.get();
            record.setDeleted(true);
            record.setDeletedAt(java.time.LocalDateTime.now());
            exampleRepository.save(record);
        }
    }

    /**
     * Lấy tất cả records bao gồm cả deleted - cần quyền đặc biệt.
     */
    @Transactional(readOnly = true)
    @DisableGlobalFilters(reason = "Administrative function to view all records including deleted")
    public List<ExampleEntity> getAllRecordsIncludingDeleted() {
        return executeWithoutFilters(() -> exampleRepository.findAll());
    }

    /**
     * Lấy records đã bị xóa của tenant hiện tại.
     */
    @Transactional(readOnly = true)
    public List<ExampleEntity> getDeletedRecords() {
        return executeWithSoftDeleteFilterOnly(() -> {
            disableGlobalFilters();
            enableFiltersForEntityClass(ExampleEntity.class);
            return exampleRepository.findDeletedRecords();
        });
    }

    /**
     * Lấy tất cả records của tất cả tenant - cần quyền super admin.
     */
    @Transactional(readOnly = true)
    @DisableGlobalFilters(
        reason = "Cross-tenant administrative report", 
        requiresSpecialPermission = true
    )
    public List<ExampleEntity> getAllRecordsAcrossAllTenants() {
        if (!hasMultiTenantAccess()) {
            throw new SecurityException("Insufficient permissions for cross-tenant access");
        }
        
        return executeWithTenantFilterOnly(() -> {
            disableGlobalFilters();
            enableFiltersForEntityClass(ExampleEntity.class);
            return exampleRepository.findAll();
        });
    }

    /**
     * Khôi phục record đã bị xóa.
     */
    @Transactional
    @DisableGlobalFilters(reason = "Recovery operation for deleted record")
    public ExampleEntity restoreDeletedRecord(UUID id) {
        return executeWithoutFilters(() -> {
            Optional<ExampleEntity> entity = exampleRepository.findById(id);
            if (entity.isPresent() && entity.get().isDeleted()) {
                ExampleEntity record = entity.get();
                record.setDeleted(false);
                record.setDeletedAt(null);
                record.setDeletedBy(null);
                return exampleRepository.save(record);
            }
            throw new IllegalArgumentException("Record not found or not deleted");
        });
    }

    /**
     * Tìm kiếm theo tên với filters tùy chỉnh.
     */
    @Transactional(readOnly = true)
    public List<ExampleEntity> searchByName(String name, boolean includeDeleted, boolean includeDisabled) {
        if (includeDeleted && includeDisabled) {
            return executeWithTenantFilterOnly(() -> 
                exampleRepository.findByNameContaining(name)
            );
        } else if (includeDeleted) {
            return executeWithCustomFilters(() -> {
                disableGlobalFilters();
                enableFiltersForEntityClass(ExampleEntity.class);
                filterHelper.enableTenantFilter(entityManager);
                filterHelper.enableEnabledFilter(entityManager);
                return exampleRepository.findByNameContaining(name);
            });
        } else if (includeDisabled) {
            return executeWithCustomFilters(() -> {
                disableGlobalFilters();
                enableFiltersForEntityClass(ExampleEntity.class);
                filterHelper.enableSoftDeleteFilter(entityManager);
                filterHelper.enableTenantFilter(entityManager);
                return exampleRepository.findByNameContaining(name);
            });
        } else {
            return executeWithFilters(() -> 
                exampleRepository.findByNameContaining(name)
            );
        }
    }

    /**
     * Thống kê số lượng records theo trạng thái.
     */
    @Transactional(readOnly = true)
    public RecordStatistics getRecordStatistics() {
        long activeCount = executeWithFilters(() -> exampleRepository.count());
        
        long deletedCount = executeWithCustomFilters(() -> {
            disableGlobalFilters();
            enableFiltersForEntityClass(ExampleEntity.class);
            filterHelper.enableTenantFilter(entityManager);
            return exampleRepository.findDeletedRecords().size();
        });
        
        long disabledCount = executeWithCustomFilters(() -> {
            disableGlobalFilters();
            enableFiltersForEntityClass(ExampleEntity.class);
            filterHelper.enableSoftDeleteFilter(entityManager);
            filterHelper.enableTenantFilter(entityManager);
            return exampleRepository.findAll().stream()
                .mapToLong(e -> e.isEnabled() ? 0 : 1)
                .sum();
        });
        
        return new RecordStatistics(activeCount, deletedCount, disabledCount);
    }

    /**
     * Record statistics DTO.
     */
    public record RecordStatistics(long activeCount, long deletedCount, long disabledCount) {}
}
