package vn.osp.common.infrastructure.filters;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.EmptyInterceptor;
import org.hibernate.Session;
import org.springframework.boot.autoconfigure.orm.jpa.HibernatePropertiesCustomizer;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * Hibernate Interceptor tự động enable global filters cho mọi session.
 * <PERSON><PERSON><PERSON> là cách tiếp cận thay thế cho SessionFactory customization.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class GlobalFilterInterceptor extends EmptyInterceptor implements HibernatePropertiesCustomizer {

    private final GlobalFilterManager filterManager;

    public void onSessionStart(Session session) {
        try {
            filterManager.enableGlobalFilters(session);
            log.debug("Global filters enabled for new session");
        } catch (Exception e) {
            log.warn("Failed to enable global filters for session: {}", e.getMessage());
        }
    }

    @Override
    public void customize(Map<String, Object> hibernateProperties) {
        // Đăng ký Global Filter Interceptor
        hibernateProperties.put("hibernate.session_factory.interceptor", this);
        log.info("Registered Global Filter Interceptor");
    }
}
