package vn.osp.common.application.constants;

/**
 * Chứa các hằng số liên quan đến HTTP được sử dụng trong toàn bộ hệ thống.
 */
public final class HttpConst {

    private HttpConst() {
        // private constructor để ngăn tạo instance
    }

    /**
     * Tên của HTTP header dùng để xác định tenant identifier trong các request API.
     */
    public static final String TENANT_ID_HEADER = "X-Tenant-Id";

    /**
     * Tên của HTTP header dùng để xác định user identifier trong các request API.
     */
    public static final String USER_ID_HEADER = "X-User-Id";

    /**
     * Tên của HTTP header dùng để xác định user roles trong các request API.
     */
    public static final String USER_ROLES_HEADER = "X-User-Roles";

    /**
     * Tên của HTTP header dùng để xác định authenticated user trong các request API.
     */
    public static final String AUTHENTICATED_USER_HEADER = "X-Authenticated-User";
}