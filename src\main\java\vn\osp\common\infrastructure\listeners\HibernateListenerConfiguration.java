package vn.osp.common.infrastructure.listeners;

import jakarta.annotation.PostConstruct;
import jakarta.persistence.EntityManagerFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.engine.spi.SessionFactoryImplementor;
import org.hibernate.event.service.spi.EventListenerRegistry;
import org.hibernate.event.spi.EventType;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;

// Warn: có thể không dùng

/**
 * Configuration class để đăng ký các Hibernate Event Listeners và Global Filters.
 */
@Configuration
@RequiredArgsConstructor
@Slf4j
public class HibernateListenerConfiguration {

    private final AuditingEventListener auditingEventListener;
    private final SoftDeleteEventListener softDeleteEventListener;
    private final MultiTenantEventListener multiTenantEventListener;

    @Lazy
    private final EntityManagerFactory entityManagerFactory;

    @PostConstruct
    public void registerListeners() {
        SessionFactoryImplementor sessionFactory = entityManagerFactory.unwrap(SessionFactoryImplementor.class);
        EventListenerRegistry registry = sessionFactory.getServiceRegistry().getService(EventListenerRegistry.class);

        // Đăng ký Auditing Event Listeners
        registry.getEventListenerGroup(EventType.PRE_INSERT).appendListener(auditingEventListener);
        registry.getEventListenerGroup(EventType.PRE_UPDATE).appendListener(auditingEventListener);

        // Đăng ký Soft Delete Event Listener
        registry.getEventListenerGroup(EventType.PRE_DELETE).appendListener(softDeleteEventListener);

        // Đăng ký Multi-Tenant Event Listeners
        registry.getEventListenerGroup(EventType.PRE_INSERT).appendListener(multiTenantEventListener);
        registry.getEventListenerGroup(EventType.PRE_UPDATE).appendListener(multiTenantEventListener);
        registry.getEventListenerGroup(EventType.PRE_DELETE).appendListener(multiTenantEventListener);

        log.info("Hibernate Event Listeners registered successfully");
        log.info("Global Filter Interceptor will be applied via HibernatePropertiesCustomizer");
    }
}
