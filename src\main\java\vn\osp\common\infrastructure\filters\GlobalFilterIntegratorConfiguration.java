package vn.osp.common.infrastructure.filters;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.boot.Metadata;
import org.hibernate.boot.spi.BootstrapContext;
import org.hibernate.engine.spi.FilterDefinition;
import org.hibernate.engine.spi.SessionFactoryImplementor;
import org.hibernate.integrator.spi.Integrator;
import org.hibernate.service.spi.SessionFactoryServiceRegistry;
import org.hibernate.type.BasicTypeRegistry;
import org.hibernate.type.SqlTypes;
import org.springframework.boot.autoconfigure.orm.jpa.HibernatePropertiesCustomizer;
import vn.osp.common.infrastructure.listeners.AuditingEventListener;
import vn.osp.common.infrastructure.listeners.MultiTenantEventListener;
import vn.osp.common.infrastructure.listeners.SoftDeleteEventListener;

import java.util.Collections;
import java.util.Map;
import java.util.UUID;

@RequiredArgsConstructor
@Slf4j
public class GlobalFilterIntegratorConfiguration implements Integrator, HibernatePropertiesCustomizer {
    private final AuditingEventListener auditingEventListener;
    private final SoftDeleteEventListener softDeleteEventListener;
    private final MultiTenantEventListener multiTenantEventListener;

    @Override
    public void integrate(
            Metadata metadata,
            BootstrapContext bootstrapContext,
            SessionFactoryImplementor sessionFactory) {

        BasicTypeRegistry typeRegistry = sessionFactory
                .getTypeConfiguration()
                .getBasicTypeRegistry();

        metadata.getFilterDefinitions().put(
                GlobalFilterManager.SOFT_DELETE_FILTER,
                new FilterDefinition(
                        GlobalFilterManager.SOFT_DELETE_FILTER,
                        "isDeleted = :isDeleted",
                        Map.of("isDeleted", typeRegistry.resolve(Boolean.class, SqlTypes.BOOLEAN).getJdbcMapping()),
                        Collections.emptyMap(),
                        true,
                        false
                )
        );

        metadata.getFilterDefinitions().put(
                GlobalFilterManager.TENANT_FILTER,
                new FilterDefinition(
                        GlobalFilterManager.TENANT_FILTER,
                        "tenantId = :tenantId OR tenantId IS NULL",
                        Map.of("tenantId", typeRegistry.resolve(UUID.class, SqlTypes.UUID).getJdbcMapping()),
                        Collections.emptyMap(),
                        true,
                        false
                )
        );

        metadata.getFilterDefinitions().put(
                GlobalFilterManager.ENABLED_FILTER,
                new FilterDefinition(
                        GlobalFilterManager.ENABLED_FILTER,
                        "enabled = :enabled",
                        Map.of("enabled", typeRegistry.resolve(Boolean.class, SqlTypes.BOOLEAN).getJdbcMapping()),
                        Collections.emptyMap(),
                        true,
                        false
                )
        );
    }

    @Override
    public void disintegrate(
            SessionFactoryImplementor sessionFactory,
            SessionFactoryServiceRegistry serviceRegistry) {

    }

    @Override
    public void customize(Map<String, Object> hibernateProperties) {
        // Đăng ký Global Filter Interceptor
        hibernateProperties.put("hibernate.integrator_filter", this);
    }
}
