package vn.osp.common.infrastructure.listeners;

import io.opentelemetry.api.baggage.Baggage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.UUID;
import java.util.function.Supplier;

/**
 * Context Provider để lấy thông tin User và Tenant từ nhiều nguồn khác nhau.
 * Ưu tiên: OpenTelemetry Baggage > SecurityContext > ThreadLocal > Default
 */
// TODO: implement khi tích hợp Spring Security
@Component
@Slf4j
public class DomainContextProvider {

    // ThreadLocal fallback cho trường hợp không có OpenTelemetry
    private static final ThreadLocal<UUID> CURRENT_USER_ID = new ThreadLocal<>();
    private static final ThreadLocal<UUID> CURRENT_TENANT_ID = new ThreadLocal<>();

    /**
     * Lấy userId hiện tại theo thứ tự ưu tiên.
     */
    public UUID getCurrentUserId() {
        try {
            // 1. Lấy từ OpenTelemetry baggage (ưu tiên cao nhất)
            String userId = Baggage.current().getEntryValue("userId");
            if (userId != null && !userId.trim().isEmpty()) {
                return UUID.fromString(userId);
            }

            // 2. Lấy từ SecurityContext (nếu có Spring Security)
            /*
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
            if (auth != null && auth.getPrincipal() instanceof UserPrincipal userPrincipal) {
                return userPrincipal.getUserId();
            }
            */

            // 3. Lấy từ ThreadLocal (fallback)
            UUID threadLocalUserId = CURRENT_USER_ID.get();
            if (threadLocalUserId != null) {
                return threadLocalUserId;
            }

        } catch (Exception ex) {
            log.warn("Failed to get current user ID: {}", ex.getMessage());
        }

        return null;
    }

    /**
     * Lấy tenantId hiện tại theo thứ tự ưu tiên.
     */
    public UUID getCurrentTenantId() {
        try {
            // 1. Lấy từ OpenTelemetry baggage (ưu tiên cao nhất)
            String tenantId = Baggage.current().getEntryValue("tenantId");
            if (tenantId != null && !tenantId.trim().isEmpty()) {
                return UUID.fromString(tenantId);
            }

            // 2. Lấy từ SecurityContext (nếu có Spring Security)
            /*
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
            if (auth != null && auth.getPrincipal() instanceof TenantAwarePrincipal principal) {
                return principal.getTenantId();
            }
            */

            // 3. Lấy từ ThreadLocal (fallback)
            UUID threadLocalTenantId = CURRENT_TENANT_ID.get();
            if (threadLocalTenantId != null) {
                return threadLocalTenantId;
            }

        } catch (Exception ex) {
            log.warn("Failed to get current tenant ID: {}", ex.getMessage());
        }

        return null;
    }

    /**
     * Set userId vào ThreadLocal (sử dụng khi không có OpenTelemetry).
     */
    public static void setCurrentUserId(UUID userId) {
        if (userId != null) {
            CURRENT_USER_ID.set(userId);
        } else {
            CURRENT_USER_ID.remove();
        }
    }

    /**
     * Set tenantId vào ThreadLocal (sử dụng khi không có OpenTelemetry).
     */
    public static void setCurrentTenantId(UUID tenantId) {
        if (tenantId != null) {
            CURRENT_TENANT_ID.set(tenantId);
        } else {
            CURRENT_TENANT_ID.remove();
        }
    }

    /**
     * Clear all ThreadLocal context.
     */
    public static void clearContext() {
        CURRENT_USER_ID.remove();
        CURRENT_TENANT_ID.remove();
    }

    /**
     * Execute một block code với specific user context.
     */
    public static <T> T executeWithUserContext(UUID userId, UUID tenantId, Supplier<T> supplier) {
        UUID previousUserId = CURRENT_USER_ID.get();
        UUID previousTenantId = CURRENT_TENANT_ID.get();

        try {
            setCurrentUserId(userId);
            setCurrentTenantId(tenantId);
            return supplier.get();
        } finally {
            setCurrentUserId(previousUserId);
            setCurrentTenantId(previousTenantId);
        }
    }

    /**
     * Execute một block code với specific user context (void return).
     */
    public static void executeWithUserContext(UUID userId, UUID tenantId, Runnable runnable) {
        executeWithUserContext(userId, tenantId, () -> {
            runnable.run();
            return null;
        });
    }
}
