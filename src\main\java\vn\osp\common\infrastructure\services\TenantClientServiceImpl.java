package vn.osp.common.infrastructure.services;

import org.springframework.stereotype.Service;
import vn.osp.common.application.models.TenantDto;
import vn.osp.common.application.services.TenantClientService;

import java.util.UUID;

@Service
public class TenantClientServiceImpl implements TenantClientService {
    @Override
    public TenantDto getTenantById(UUID id) {
        // todo: implement actual call to tenant service
        return null;
    }

    @Override
    public TenantDto getTenantByName(String name) {
        // todo: implement actual call to tenant service
        return null;
    }
}
