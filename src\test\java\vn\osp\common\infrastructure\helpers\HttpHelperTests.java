package vn.osp.common.infrastructure.helpers;

import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringReader;
import java.util.Collections;
import java.util.Enumeration;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests cho HttpHelper class.
 */
@ExtendWith(MockitoExtension.class)
class HttpHelperTests {

    @Mock
    private HttpServletRequest request;

    @Mock
    private HttpServletResponse response;

    @Mock
    private PrintWriter printWriter;

    @BeforeEach
    void setUp() throws IOException {
        when(response.getWriter()).thenReturn(printWriter);
    }

    // ==========================
    // Headers Tests
    // ==========================

    @Test
    void getHeader_ShouldReturnHeaderValue_WhenHeaderExists() {
        // Given
        String headerName = "Content-Type";
        String headerValue = "application/json";
        when(request.getHeader(headerName)).thenReturn(headerValue);

        // When
        String result = HttpHelper.getHeader(request, headerName, "default");

        // Then
        assertEquals(headerValue, result);
    }

    @Test
    void getHeader_ShouldReturnDefaultValue_WhenHeaderNotExists() {
        // Given
        String headerName = "Missing-Header";
        String defaultValue = "default-value";
        when(request.getHeader(headerName)).thenReturn(null);

        // When
        String result = HttpHelper.getHeader(request, headerName, defaultValue);

        // Then
        assertEquals(defaultValue, result);
    }

    @Test
    void getHeaders_ShouldReturnAllHeaders() {
        // Given
        Enumeration<String> headerNames = Collections.enumeration(
                java.util.List.of("Content-Type", "Authorization")
        );
        when(request.getHeaderNames()).thenReturn(headerNames);
        when(request.getHeader("Content-Type")).thenReturn("application/json");
        when(request.getHeader("Authorization")).thenReturn("Bearer token");

        // When
        Map<String, String> result = HttpHelper.getHeaders(request);

        // Then
        assertEquals(2, result.size());
        assertEquals("application/json", result.get("Content-Type"));
        assertEquals("Bearer token", result.get("Authorization"));
    }

    // ==========================
    // URL / Domain Tests
    // ==========================

    @Test
    void getBaseUrl_ShouldReturnHttpUrl_WhenStandardPort() {
        // Given
        when(request.getScheme()).thenReturn("http");
        when(request.getServerName()).thenReturn("localhost");
        when(request.getServerPort()).thenReturn(80);

        // When
        String result = HttpHelper.getBaseUrl(request);

        // Then
        assertEquals("http://localhost", result);
    }

    @Test
    void getBaseUrl_ShouldReturnHttpsUrl_WhenStandardPort() {
        // Given
        when(request.getScheme()).thenReturn("https");
        when(request.getServerName()).thenReturn("example.com");
        when(request.getServerPort()).thenReturn(443);

        // When
        String result = HttpHelper.getBaseUrl(request);

        // Then
        assertEquals("https://example.com", result);
    }

    @Test
    void getBaseUrl_ShouldIncludePort_WhenNonStandardPort() {
        // Given
        when(request.getScheme()).thenReturn("http");
        when(request.getServerName()).thenReturn("localhost");
        when(request.getServerPort()).thenReturn(8080);

        // When
        String result = HttpHelper.getBaseUrl(request);

        // Then
        assertEquals("http://localhost:8080", result);
    }

    @Test
    void getFullUrl_ShouldReturnUrlWithoutQueryString_WhenNoQueryString() {
        // Given
        StringBuffer requestUrl = new StringBuffer("http://localhost:8080/api/test");
        when(request.getRequestURL()).thenReturn(requestUrl);
        when(request.getQueryString()).thenReturn(null);

        // When
        String result = HttpHelper.getFullUrl(request);

        // Then
        assertEquals("http://localhost:8080/api/test", result);
    }

    @Test
    void getFullUrl_ShouldReturnUrlWithQueryString_WhenQueryStringExists() {
        // Given
        StringBuffer requestUrl = new StringBuffer("http://localhost:8080/api/test");
        when(request.getRequestURL()).thenReturn(requestUrl);
        when(request.getQueryString()).thenReturn("param1=value1&param2=value2");

        // When
        String result = HttpHelper.getFullUrl(request);

        // Then
        assertEquals("http://localhost:8080/api/test?param1=value1&param2=value2", result);
    }

    // ==========================
    // Client Info Tests
    // ==========================

    @Test
    void getClientIp_ShouldReturnXForwardedFor_WhenHeaderExists() {
        // Given
        String xForwardedFor = "*************";
        when(request.getHeader("X-Forwarded-For")).thenReturn(xForwardedFor);
        when(request.getRemoteAddr()).thenReturn("127.0.0.1");

        // When
        String result = HttpHelper.getClientIp(request);

        // Then
        assertEquals(xForwardedFor, result);
    }

    @Test
    void getClientIp_ShouldReturnFirstIp_WhenMultipleIpsInXForwardedFor() {
        // Given
        String xForwardedFor = "*************, ********, **********";
        when(request.getHeader("X-Forwarded-For")).thenReturn(xForwardedFor);

        // When
        String result = HttpHelper.getClientIp(request);

        // Then
        assertEquals("*************", result);
    }

    @Test
    void getClientIp_ShouldReturnRemoteAddr_WhenXForwardedForNotExists() {
        // Given
        String remoteAddr = "127.0.0.1";
        when(request.getHeader("X-Forwarded-For")).thenReturn(null);
        when(request.getRemoteAddr()).thenReturn(remoteAddr);

        // When
        String result = HttpHelper.getClientIp(request);

        // Then
        assertEquals(remoteAddr, result);
    }

    @Test
    void getUserAgent_ShouldReturnUserAgent() {
        // Given
        String userAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64)";
        when(request.getHeader("User-Agent")).thenReturn(userAgent);

        // When
        String result = HttpHelper.getUserAgent(request);

        // Then
        assertEquals(userAgent, result);
    }

    @Test
    void getUserAgent_ShouldReturnNull_WhenUserAgentNotExists() {
        // Given
        when(request.getHeader("User-Agent")).thenReturn(null);

        // When
        String result = HttpHelper.getUserAgent(request);

        // Then
        assertNull(result);
    }

    // ==========================
    // Cookies Tests
    // ==========================

    @Test
    void getCookie_ShouldReturnCookieValue_WhenCookieExists() {
        // Given
        String cookieName = "sessionId";
        String cookieValue = "abc123";
        Cookie[] cookies = {
                new Cookie(cookieName, cookieValue),
                new Cookie("otherId", "xyz789")
        };
        when(request.getCookies()).thenReturn(cookies);

        // When
        String result = HttpHelper.getCookie(request, cookieName);

        // Then
        assertEquals(cookieValue, result);
    }

    @Test
    void getCookie_ShouldReturnNull_WhenCookieNotExists() {
        // Given
        String cookieName = "missingCookie";
        Cookie[] cookies = {
                new Cookie("sessionId", "abc123"),
                new Cookie("otherId", "xyz789")
        };
        when(request.getCookies()).thenReturn(cookies);

        // When
        String result = HttpHelper.getCookie(request, cookieName);

        // Then
        assertNull(result);
    }

    @Test
    void getCookie_ShouldReturnNull_WhenNoCookies() {
        // Given
        when(request.getCookies()).thenReturn(null);

        // When
        String result = HttpHelper.getCookie(request, "anyCookie");

        // Then
        assertNull(result);
    }

    @Test
    void addCookie_ShouldAddCookieToResponse() {
        // Given
        String name = "sessionId";
        String value = "abc123";
        int maxAge = 3600;

        // When
        HttpHelper.addCookie(response, name, value, maxAge);

        // Then
        verify(response).addCookie(argThat(cookie -> 
                cookie.getName().equals(name) &&
                cookie.getValue().equals(value) &&
                cookie.getMaxAge() == maxAge &&
                cookie.getPath().equals("/") &&
                cookie.isHttpOnly()
        ));
    }

    @Test
    void deleteCookie_ShouldDeleteCookieFromResponse() {
        // Given
        String name = "sessionId";

        // When
        HttpHelper.deleteCookie(response, name);

        // Then
        verify(response).addCookie(argThat(cookie -> 
                cookie.getName().equals(name) &&
                cookie.getValue() == null &&
                cookie.getMaxAge() == 0 &&
                cookie.getPath().equals("/")
        ));
    }

    // ==========================
    // Request Body Tests
    // ==========================

    @Test
    void getBody_ShouldReturnRequestBody() throws IOException {
        // Given
        String requestBody = "{\"name\":\"test\",\"value\":\"data\"}";
        BufferedReader reader = new BufferedReader(new StringReader(requestBody));
        when(request.getReader()).thenReturn(reader);

        // When
        String result = HttpHelper.getBody(request);

        // Then
        assertEquals(requestBody, result);
    }

    @Test
    void getBody_ShouldReturnEmptyString_WhenBodyIsEmpty() throws IOException {
        // Given
        BufferedReader reader = new BufferedReader(new StringReader(""));
        when(request.getReader()).thenReturn(reader);

        // When
        String result = HttpHelper.getBody(request);

        // Then
        assertEquals("", result);
    }

    @Test
    void getBody_ShouldThrowIOException_WhenReaderFails() throws IOException {
        // Given
        when(request.getReader()).thenThrow(new IOException("Reader error"));

        // When & Then
        assertThrows(IOException.class, () -> HttpHelper.getBody(request));
    }

    // ==========================
    // Response Helpers Tests
    // ==========================

    @Test
    void writeJson_ShouldWriteJsonToResponse() throws IOException {
        // Given
        Object testObject = Map.of("key", "value", "number", 123);
        
        // When
        HttpHelper.writeJson(response, testObject);

        // Then
        verify(response).setContentType("application/json;charset=UTF-8");
        verify(response).getWriter();
    }

    @Test
    void writeJson_ShouldThrowIOException_WhenWriterFails() throws IOException {
        // Given
        Object testObject = Map.of("key", "value");
        when(response.getWriter()).thenThrow(new IOException("Writer error"));

        // When & Then
        assertThrows(IOException.class, () -> HttpHelper.writeJson(response, testObject));
    }

    @Test
    void writeText_ShouldWriteTextToResponse() throws IOException {
        // Given
        String text = "Hello World";

        // When
        HttpHelper.writeText(response, text);

        // Then
        verify(response).setContentType("text/plain;charset=UTF-8");
        verify(printWriter).write(text);
    }

    @Test
    void writeText_ShouldThrowIOException_WhenWriterFails() throws IOException {
        // Given
        String text = "Hello World";
        when(response.getWriter()).thenThrow(new IOException("Writer error"));

        // When & Then
        assertThrows(IOException.class, () -> HttpHelper.writeText(response, text));
    }
}